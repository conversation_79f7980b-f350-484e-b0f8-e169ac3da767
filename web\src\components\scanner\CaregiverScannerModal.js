import React, { useState } from 'react';
import './caregiverScannerModal.css';

const CaregiverScannerModal = ({ 
  visible, 
  onClose, 
  onSuccess, 
  scannerTitle = "Scan Caregiver", 
  patientId 
}) => {
  const supervisorColors = {
    primary: 'rgb(255, 0, 242)'
  };

  const handleScanQRCode = () => {
    onClose();
    // Navigate to the QR code scanner screen
    // In React web, you would use react-router or similar navigation
    console.log('Navigate to QR scanner with patientId:', patientId);
  };

  const handleEnterCode = () => {
    onClose();
    // Navigate to the manual code entry screen
    // In React web, you would use react-router or similar navigation
    console.log('Navigate to manual code entry with patientId:', patientId);
  };

  if (!visible) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="header">
          <h2 className="title">{scannerTitle}</h2>
          <button className="close-button" onClick={onClose}>
            <span className="close-icon">×</span>
          </button>
        </div>

        <div className="options-container">
          <div className="option" onClick={handleScanQRCode}>
            <div className="icon-container qr-icon">
              <span className="icon">📱</span>
            </div>
            <div className="option-text-container">
              <h3 className="option-title">Scan QR Code</h3>
              <p className="option-description">
                Use camera to scan caregiver's QR code
              </p>
            </div>
            <span className="chevron">›</span>
          </div>

          <div className="divider" />

          <div className="option" onClick={handleEnterCode}>
            <div className="icon-container keypad-icon">
              <span className="icon">⌨️</span>
            </div>
            <div className="option-text-container">
              <h3 className="option-title">Enter Code Manually</h3>
              <p className="option-description">
                Type the 8-character caregiver code
              </p>
            </div>
            <span className="chevron">›</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaregiverScannerModal;
